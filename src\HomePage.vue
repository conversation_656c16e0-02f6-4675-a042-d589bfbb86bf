<template>
  <div class="app-container">
    <div class="header">
      <h1>高级曲线路径生成器</h1>
      <p>使用优化的样条曲线算法生成流畅自然的路径，支持动态节点管理和动画效果</p>
    </div>
    <div class="path-generator">
      <div class="controls">
        <div class="control-group">
          <label>
            节点数量:
            <span class="value-display">{{ nodeCount }}</span>
          </label>
          <input type="range" min="3" max="50" v-model.number="nodeCount">
        </div>
        <div class="control-group">
          <label>
            曲线幅度:
            <span class="value-display">{{ amplitude }}</span>
          </label>
          <input type="range" min="50" max="300" v-model.number="amplitude">
        </div>
        <div class="control-group">
          <label>
            曲线频率:
            <span class="value-display">{{ frequency.toFixed(1) }}</span>
          </label>
          <input type="range" min="0.5" max="5" step="0.1" v-model.number="frequency">
        </div>
        <div class="button-group">
          <button @click="generatePath">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            重新生成路径
          </button>
        </div>
      </div>
      <div class="svg-container">
        <svg :width="canvasWidth" :height="canvasHeight" ref="svgRef">
          <g>
            <line v-for="gx in gridCountX" :key="'gx'+gx"
              :x1="gx * gridSize" y1="0"
              :x2="gx * gridSize" :y2="canvasHeight"
              class="grid-line"
            />
            <line v-for="gy in gridCountY" :key="'gy'+gy"
              x1="0" :y1="gy * gridSize"
              :x2="canvasWidth" :y2="gy * gridSize"
              class="grid-line"
            />
          </g>
          <!-- 主路径 -->
          <path
            v-if="pathD"
            :d="pathD"
            stroke="#4a5568"
            stroke-width="4"
            fill="none"
            stroke-linecap="round"
          />
          <path
            v-if="pathD"
            :d="pathD"
            ref="progressPath"
            stroke="#9b5de5"
            stroke-width="4"
            fill="none"
            stroke-linecap="round"
            :style="{
              strokeDasharray: pathLength > 0 ? pathLength : 'none',
              strokeDashoffset: pathLength > 0 ? progressStrokeDashoffset : 0
            }"
          />
          <g v-for="(pt, idx) in pathPoints" :key="'node-'+idx">
            <circle
              :cx="pt[0]"
              :cy="pt[1]"
              :r="getNodeRadius(idx)"
              :fill="getNodeColor(idx)"
              stroke="#1a202c"
              stroke-width="1.5"
              @mouseover="hoverIndex = idx"
              @mouseout="hoverIndex = null"
              class="node-circle"
              :class="{
                'node-pending': nodeStates[idx] === NODE_STATES.PENDING,
                'node-in-progress': nodeStates[idx] === NODE_STATES.IN_PROGRESS,
                'node-completed': nodeStates[idx] === NODE_STATES.COMPLETED
              }"
            />
            <text
              :x="pt[0] + 12"
              :y="pt[1] - 15"
              class="node-label"
            >{{ idx + 1 }}</text>
            <text
              v-if="hoverIndex === idx"
              :x="pt[0] + 15"
              :y="pt[1] + 25"
              font-size="11"
              fill="#cbd5e0"
            >({{ pt[0].toFixed(0) }}, {{ pt[1].toFixed(0) }})</text>
          </g>
        </svg>
      </div>
      <div class="node-controls">
        <div class="status-info">
          <div class="status-legend">
            <div class="legend-section">
              <h4>节点状态:</h4>
              <span class="legend-item">
                <span class="color-dot" style="background-color: #718096;"></span>
                未进行
              </span>
              <span class="legend-item">
                <span class="color-dot" style="background-color: #f6ad55;"></span>
                正在进行
              </span>
              <span class="legend-item">
                <span class="color-dot" style="background-color: #68d391;"></span>
                已完成
              </span>
            </div>
            <div class="legend-section">
              <h4>路径颜色:</h4>
              <span class="legend-item">
                <span class="color-line" style="background-color: #9b5de5;"></span>
                已完成路径
              </span>
              <span class="legend-item">
                <span class="color-line" style="background-color: #4a5568;"></span>
                未完成路径
              </span>
            </div>
          </div>
          <div class="current-node-info">
            当前节点: {{ currentNodeIndex + 1 }} / {{ nodeCount }}
          </div>
        </div>
        <div class="control-buttons">
          <button @click="completeCurrentNode" :disabled="!canCompleteCurrentNode" class="complete-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M5 13l4 4L19 7"></path>
            </svg>
            完成当前节点
          </button>
          <button @click="resetNodeStates" class="reset-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M2.5 2v6h6M21.5 22v-6h-6"></path>
              <path d="M22 11.5A10 10 0 003.2 7.2M2 12.5a10 10 0 0018.8 4.2"></path>
            </svg>
            重置所有状态
          </button>
        </div>
      </div>
      <div class="performance-info">
        <div class="perf-item">
          <div class="perf-value">{{ generationTime.toFixed(1) }}ms</div>
          <div class="perf-label">生成时间</div>
        </div>
        <div class="perf-item">
          <div class="perf-value">{{ pathPoints.length }}</div>
          <div class="perf-label">节点数量</div>
        </div>
        <div class="perf-item">
          <div class="perf-value">{{ pathLength.toFixed(0) }}px</div>
          <div class="perf-label">路径长度</div>
        </div>
        <div class="perf-item">
          <div class="perf-value">{{ renderCount }}</div>
          <div class="perf-label">渲染次数</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import gsap from 'gsap'

// 配置参数
const amplitude = ref(180)
const frequency = ref(2.5)
const nodeCount = ref(15)
const canvasWidth = ref(950)
const canvasHeight = ref(450)
const gridSize = 40

// 性能指标
const generationTime = ref(0)
const renderCount = ref(0)

// 网格数量
const gridCountX = computed(() => Math.ceil(canvasWidth.value / gridSize))
const gridCountY = computed(() => Math.ceil(canvasHeight.value / gridSize))

// 节点状态定义
const NODE_STATES = {
  PENDING: 'pending',
  IN_PROGRESS: 'progress',
  COMPLETED: 'completed'
}

// 响应式数据
const nodeStates = ref([])
const currentNodeIndex = ref(0)
const hoverIndex = ref(null)
const nodePathLengths = ref([])
const pathPoints = ref([])

// DOM refs
const svgRef = ref(null)
const progressPath = ref(null)
const pathLength = ref(0)
const progressStrokeDashoffset = ref(0)

// 样条插值
function generatePathPoints() {
  const startTime = performance.now()
  const points = []
  const centerY = canvasHeight.value / 2

  // 确保至少有3个节点
  const actualNodeCount = Math.max(3, nodeCount.value)

  // 生成控制点
  const controlPoints = Array.from({ length: actualNodeCount + 2 }, (_, i) => {
    const x = (i - 1) * (canvasWidth.value / (actualNodeCount - 1))
    const progress = (i / (actualNodeCount + 1)) * Math.PI * 2 * frequency.value
    const y = centerY + Math.sin(progress) * amplitude.value
    return [x, y]
  })

  // 生成路径点
  for (let i = 0; i < actualNodeCount; i++) {
    const t = i / (actualNodeCount - 1)
    const index = Math.floor(t * (controlPoints.length - 3)) + 1
    const p0 = controlPoints[index - 1] || controlPoints[0]
    const p1 = controlPoints[index] || controlPoints[0]
    const p2 = controlPoints[index + 1] || controlPoints[controlPoints.length - 1]
    const p3 = controlPoints[index + 2] || controlPoints[controlPoints.length - 1]
    const t2 = t * (controlPoints.length - 3) - (index - 1)
    const x = interpolateCatmullRom(p0[0], p1[0], p2[0], p3[0], t2)
    const y = interpolateCatmullRom(p0[1], p1[1], p2[1], p3[1], t2)
    points.push([Math.max(0, Math.min(canvasWidth.value, x)), Math.max(0, Math.min(canvasHeight.value, y))])
  }

  generationTime.value = performance.now() - startTime
  return points
}
function interpolateCatmullRom(p0, p1, p2, p3, t) {
  const v0 = (p2 - p0) * 0.5
  const v1 = (p3 - p1) * 0.5
  const t2 = t * t
  const t3 = t * t2
  return (2 * p1 - 2 * p2 + v0 + v1) * t3 +
         (-3 * p1 + 3 * p2 - 2 * v0 - v1) * t2 +
         v0 * t + p1
}
// 转贝塞尔
function pointsToBezierPath(points) {
  if (points.length < 2) {
    console.log('路径点不足，无法生成路径')
    return ''
  }

  let d = `M${points[0][0].toFixed(2)},${points[0][1].toFixed(2)}`

  for (let i = 0; i < points.length - 1; i++) {
    const start = points[i]
    const end = points[i + 1]
    const dx = end[0] - start[0]
    const dy = end[1] - start[1]
    const distance = Math.sqrt(dx * dx + dy * dy) * 0.4

    const prevPoint = points[i - 1] || start
    const nextPoint = points[i + 2] || end

    const startAngle = Math.atan2(end[1] - prevPoint[1], end[0] - prevPoint[0])
    const endAngle = Math.atan2(nextPoint[1] - start[1], nextPoint[0] - start[0])

    const ctrl1 = [
      start[0] + Math.cos(startAngle) * distance,
      start[1] + Math.sin(startAngle) * distance
    ]
    const ctrl2 = [
      end[0] - Math.cos(endAngle) * distance,
      end[1] - Math.sin(endAngle) * distance
    ]

    d += ` C${ctrl1[0].toFixed(2)},${ctrl1[1].toFixed(2)},${ctrl2[0].toFixed(2)},${ctrl2[1].toFixed(2)},${end[0].toFixed(2)},${end[1].toFixed(2)}`
  }

  return d
}
const pathD = computed(() => {
  const path = pointsToBezierPath(pathPoints.value)
  return path
})

watch(pathPoints, () => {
  renderCount.value++
})

// 路径长度与节点分布 - 优化版本
function calcPathLengthsForNodes() {
  if (!progressPath.value) return
  const startTime = performance.now()
  const pathElem = progressPath.value
  const totalLen = pathElem.getTotalLength()
  const nodeLens = []

  // 更精确的节点位置查找算法
  const findNearestLength = (tx, ty, searchStart = 0, searchEnd = totalLen) => {
    const sampleCount = 200 // 增加采样点数量
    const step = (searchEnd - searchStart) / sampleCount
    let bestL = searchStart
    let bestDist = Infinity

    // 第一轮：粗略搜索
    for (let i = 0; i <= sampleCount; i++) {
      const l = searchStart + i * step
      const point = pathElem.getPointAtLength(l)
      const dist = Math.sqrt((point.x - tx) ** 2 + (point.y - ty) ** 2)

      if (dist < bestDist) {
        bestDist = dist
        bestL = l
      }
    }

    // 第二轮：精细搜索
    const fineSearchRange = step * 2
    const fineStart = Math.max(searchStart, bestL - fineSearchRange)
    const fineEnd = Math.min(searchEnd, bestL + fineSearchRange)
    const fineStep = (fineEnd - fineStart) / 100

    for (let i = 0; i <= 100; i++) {
      const l = fineStart + i * fineStep
      const point = pathElem.getPointAtLength(l)
      const dist = Math.sqrt((point.x - tx) ** 2 + (point.y - ty) ** 2)

      if (dist < bestDist) {
        bestDist = dist
        bestL = l
      }
    }

    return bestL
  }

  // 计算每个节点在路径上的位置
  let lastLength = 0
  for (let i = 0; i < pathPoints.value.length; i++) {
    const [tx, ty] = pathPoints.value[i]
    // 扩大搜索范围以确保找到正确位置
    const searchStart = Math.max(0, lastLength - 100)
    const searchEnd = Math.min(totalLen, lastLength + 200)
    const len = findNearestLength(tx, ty, searchStart, searchEnd)
    nodeLens.push(len)
    lastLength = len
  }

  nodePathLengths.value = nodeLens
  console.log('节点路径长度:', nodeLens.map(l => l.toFixed(1)))
  console.log('总路径长度:', totalLen.toFixed(1))

  // 验证节点位置是否正确
  nodeLens.forEach((len, i) => {
    const point = pathElem.getPointAtLength(len)
    const [nodeX, nodeY] = pathPoints.value[i]
    const distance = Math.sqrt((point.x - nodeX) ** 2 + (point.y - nodeY) ** 2)
    console.log(`节点 ${i}: 路径位置(${point.x.toFixed(1)}, ${point.y.toFixed(1)}) vs 节点位置(${nodeX.toFixed(1)}, ${nodeY.toFixed(1)}) 距离: ${distance.toFixed(1)}`)
  })

  generationTime.value = performance.now() - startTime
}
// 生成路径并初始化
async function generatePath() {
  pathPoints.value = generatePathPoints()

  await nextTick()

  // 等待DOM更新后再计算路径长度
  setTimeout(async () => {
    if (progressPath.value) {
      try {
        pathLength.value = progressPath.value.getTotalLength()
        progressStrokeDashoffset.value = pathLength.value
        calcPathLengthsForNodes()
        initializeNodeStates()
      } catch (error) {
        console.error('计算路径长度时出错:', error)
      }
    } else {
      console.error('progressPath.value 为空')
    }
  }, 100)
}
function initializeNodeStates() {
  nodeStates.value = Array(nodeCount.value).fill(NODE_STATES.PENDING)
  nodeStates.value[0] = NODE_STATES.IN_PROGRESS
  currentNodeIndex.value = 0
  if (pathLength.value > 0) {
    // 初始时显示完整的灰色路径，紫色路径从0开始
    progressStrokeDashoffset.value = pathLength.value
  }
}
const getNodeColor = idx => {
  const state = nodeStates.value[idx]
  switch (state) {
    case NODE_STATES.PENDING: return '#718096'
    case NODE_STATES.IN_PROGRESS: return '#f6ad55'
    case NODE_STATES.COMPLETED: return '#68d391'
    default: return '#718096'
  }
}
const getNodeRadius = idx => {
  const baseRadius = 6
  if (nodeStates.value[idx] === NODE_STATES.IN_PROGRESS) return 10
  if (nodeStates.value[idx] === NODE_STATES.COMPLETED) return 8
  return baseRadius
}
// 动画 - 优化版本
function animatePathAndNode(segmentIndex, nextNodeIndex) {
  if (
    nodePathLengths.value[segmentIndex] === undefined ||
    nodePathLengths.value[nextNodeIndex] === undefined
  ) {
    console.error('节点路径长度未定义:', segmentIndex, nextNodeIndex)
    return
  }

  const from = nodePathLengths.value[segmentIndex]
  const to = nodePathLengths.value[nextNodeIndex]

  console.log(`动画从节点 ${segmentIndex} 到节点 ${nextNodeIndex}:`,
    `从 ${from.toFixed(1)} 到 ${to.toFixed(1)}`)
  console.log(`总路径长度: ${pathLength.value.toFixed(1)}`)
  console.log(`起始偏移: ${(pathLength.value - from).toFixed(1)}`)
  console.log(`目标偏移: ${(pathLength.value - to).toFixed(1)}`)

  // 确保起始位置正确
  progressStrokeDashoffset.value = pathLength.value - from

  // 使用更平滑的动画，确保到达准确位置
  gsap.to(progressStrokeDashoffset, {
    value: pathLength.value - to,
    duration: 1.5, // 稍微延长动画时间以确保平滑
    ease: "power2.inOut", // 更平滑的缓动
    onUpdate: () => {
      // 添加一些关键点的调试信息
      const currentOffset = progressStrokeDashoffset.value
      const currentProgress = pathLength.value - currentOffset
      if (Math.abs(currentProgress - to) < 10) {
        console.log(`接近目标: 当前进度 ${currentProgress.toFixed(1)}, 目标 ${to.toFixed(1)}`)
      }
    },
    onComplete: () => {
      // 确保最终位置准确
      progressStrokeDashoffset.value = pathLength.value - to
      nodeStates.value[nextNodeIndex] = NODE_STATES.IN_PROGRESS
      console.log(`动画完成，最终偏移: ${progressStrokeDashoffset.value.toFixed(1)}, 节点 ${nextNodeIndex} 设为进行中`)
    }
  })
}
// 节点完成
function completeCurrentNode() {
  if (currentNodeIndex.value < nodeStates.value.length) {
    const curr = currentNodeIndex.value
    nodeStates.value[curr] = NODE_STATES.COMPLETED
    if (curr < nodeStates.value.length - 1) {
      animatePathAndNode(curr, curr + 1)
      currentNodeIndex.value++
    }
  }
}
function resetNodeStates() {
  generatePath()
}
const canCompleteCurrentNode = computed(() =>
  currentNodeIndex.value < nodeStates.value.length &&
  nodeStates.value[currentNodeIndex.value] === NODE_STATES.IN_PROGRESS &&
  nodePathLengths.value.length === pathPoints.value.length
)
// 监听
watch([nodeCount, amplitude, frequency], generatePath)
onMounted(() => {
  // 延迟一点时间确保DOM完全渲染
  setTimeout(() => {
    generatePath()
  }, 200)
})
</script>

<!-- 样式与原始 CSS 完全一致，请将 .css 代码拷贝到父级全局样式文件或 <style scoped> -->
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
      color: #fff;
      min-height: 100vh;
      padding: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .app-container {
      width: 100%;
      max-width: 1000px;
      margin: 0 auto;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    .header h1 {
      font-size: 2.8rem;
      margin-bottom: 10px;
      text-shadow: 0 2px 8px rgba(0,0,0,0.3);
      background: linear-gradient(45deg, #ff8a00, #e52e71);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .header p {
      font-size: 1.2rem;
      opacity: 0.9;
      max-width: 700px;
      margin: 0 auto;
      line-height: 1.6;
    }
    .path-generator {
      background: rgba(25, 25, 50, 0.8);
      border-radius: 16px;
      padding: 25px;
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .controls {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
      padding: 1.5rem;
      background: rgba(40, 40, 70, 0.7);
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .control-group {
      display: flex;
      flex-direction: column;
      gap: 0.8rem;
      min-width: 200px;
      flex: 1;
    }
    .control-group label {
      font-weight: 600;
      color: #a0a0ff;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .control-group input[type="range"] {
      width: 100%;
      height: 8px;
      background: rgba(100, 100, 180, 0.4);
      border-radius: 4px;
      outline: none;
      -webkit-appearance: none;
    }
    .control-group input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 22px;
      height: 22px;
      border-radius: 50%;
      background: #6a5af9;
      cursor: pointer;
      box-shadow: 0 0 8px rgba(106, 90, 249, 0.7);
    }
    .value-display {
      background: rgba(80, 80, 150, 0.4);
      padding: 4px 12px;
      border-radius: 20px;
      min-width: 50px;
      text-align: center;
      font-weight: bold;
    }
    .button-group {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }
    button {
      padding: 0.8rem 1.5rem;
      border: none;
      border-radius: 50px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;
      transition: all 0.3s ease;
      background: linear-gradient(to right, #6a5af9, #d66efd);
      color: white;
      box-shadow: 0 4px 15px rgba(106, 90, 249, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    button:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(106, 90, 249, 0.5);
    }
    button:active {
      transform: translateY(1px);
    }
    button:disabled {
      background: #555;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .svg-container {
      position: relative;
      background: rgba(20, 20, 40, 0.7);
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.1);
      height: 450px;
    }
    svg {
      width: 100%;
      height: 100%;
      display: block;
    }
    .node-controls {
      margin-top: 1.5rem;
      padding: 1.5rem;
      background: rgba(40, 40, 70, 0.7);
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .status-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      flex-wrap: wrap;
      gap: 1.5rem;
    }
    .status-legend {
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;
      align-items: flex-start;
    }
    .legend-section {
      display: flex;
      flex-direction: column;
      gap: 0.8rem;
    }
    .legend-section h4 {
      margin: 0;
      font-size: 1.1rem;
      color: #a0a0ff;
      font-weight: bold;
    }
    .legend-section .legend-item { 
      margin-left: 0.5rem; 
    }
    .legend-item {
      display: flex;
      align-items: center;
      gap: 0.8rem;
      font-size: 0.95rem;
    }
    .color-dot {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    .color-line {
      width: 30px;
      height: 4px;
      border-radius: 2px;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    .current-node-info {
      font-weight: bold;
      color: #ffcc00;
      font-size: 1.2rem;
      background: rgba(0, 0, 0, 0.3);
      padding: 0.8rem 1.5rem;
      border-radius: 50px;
    }
    .control-buttons {
      display: flex;
      gap: 1.5rem;
      flex-wrap: wrap;
    }
    .complete-btn {
      background: linear-gradient(to right, #00c853, #00e676);
    }
    .reset-btn {
      background: linear-gradient(to right, #ff416c, #ff4b2b);
    }
    .performance-info {
      margin-top: 1.5rem;
      padding: 1rem;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 8px;
      display: flex;
      gap: 1.5rem;
      flex-wrap: wrap;
    }
    .perf-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      min-width: 120px;
    }
    .perf-value {
      font-size: 1.8rem;
      font-weight: bold;
      color: #00e5ff;
    }
    .perf-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }
    .grid-line {
      stroke: rgba(255, 255, 255, 0.07);
      stroke-width: 1;
    }
    .node-label {
      font-size: 12px;
      fill: #ffcc00;
      font-weight: bold;
      text-shadow: 0 1px 2px rgba(0,0,0,0.7);
    }
    @media (max-width: 768px) {
      .controls, .control-buttons, .status-info {
        flex-direction: column;
        align-items: stretch;
      }
      .header h1 {
        font-size: 2.2rem;
      }
    }
  </style>